<template>
  <div class="lawyer-page-container">
    <div class="lawyer-content-wrapper">
      <!-- 数据表格 -->
      <a-card class="lawyer-table-card" :bordered="false">
        <!-- 搜索和筛选区域 -->
        <div class="lawyer-search-form">
          <a-row :gutter="16">
            <a-col :span="4">
              <a-input
                v-model="searchText"
                placeholder="网站名称或代码"
                allowClear
                @keyup.enter="onSearch"
              />
            </a-col>
            <a-col :span="4">
              <div class="lawyer-width-100">
                <a-select
                  v-model="filterEnabled"
                  placeholder="启用状态"
                  allowClear
                  @change="onSearch"
                >
                  <a-select-option :value="1">启用</a-select-option>
                  <a-select-option :value="0">禁用</a-select-option>
                </a-select>
              </div>
            </a-col>
            <a-col :span="8" class="lawyer-search-buttons">
              <div class="lawyer-button-group">
                <a-button
                  type="primary"
                  @click="onSearch"
                  :loading="tableLoading"
                >
                  搜索
                </a-button>
                <a-button @click="onReset"> 重置 </a-button>
                <a-button type="primary" @click="showAddModal">
                  <a-icon type="plus" />
                  新增配置
                </a-button>
              </div>
            </a-col>
          </a-row>
        </div>

        <a-table
          :columns="columns"
          :data-source="configList"
          :pagination="pagination"
          :loading="tableLoading"
          :rowKey="(record) => record.id"
          @change="handleTableChange"
        >
        <!-- 网站名称列 -->
        <template slot="websiteName" slot-scope="text, record">
          <div>
            <div class="lawyer-table-title">{{ record.websiteName }}</div>
            <div class="lawyer-table-subtitle">{{ record.websiteCode }}</div>
          </div>
        </template>

        <!-- 网站URL列 -->
        <template slot="websiteUrl" slot-scope="text">
          <a :href="text" target="_blank" class="lawyer-link">{{ text }}</a>
        </template>

        <!-- 启用状态列 -->
        <template slot="enabled" slot-scope="text, record">
          <a-switch
            :checked="text === 1"
            :loading="record.switchLoading"
            @change="(checked) => toggleEnabled(record, checked)"
          />
        </template>

        <!-- 创建时间列 -->
        <template slot="createdTime" slot-scope="text">
          {{ formatTime(text) }}
        </template>

        <!-- 操作列 -->
        <template slot="action" slot-scope="text, record">
          <div class="lawyer-action-links">
            <a @click="editConfig(record)" class="lawyer-link-edit">编辑</a>
            <a @click="deleteConfig(record)" class="lawyer-link-delete">删除</a>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 新增/编辑配置模态框 -->
    <a-modal
      :title="modalTitle"
      :visible="modalVisible"
      :width="600"
      :confirmLoading="modalLoading"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      okText="确认"
      cancelText="取消"
    >
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="网站名称">
          <a-input
            v-decorator="[
              'websiteName',
              {
                rules: [{ required: true, message: '请输入网站名称' }],
              },
            ]"
            placeholder="请输入网站名称"
          />
        </a-form-item>

        <a-form-item label="网站代码">
          <a-input
            v-decorator="[
              'websiteCode',
              {
                rules: [{ required: true, message: '请输入网站代码' }],
              },
            ]"
            placeholder="请输入网站代码"
          />
        </a-form-item>

        <a-form-item label="网站URL">
          <a-input
            v-decorator="[
              'websiteUrl',
              {
                rules: [
                  { required: true, message: '请输入网站URL' },
                  { type: 'url', message: '请输入有效的URL' },
                ],
              },
            ]"
            placeholder="请输入网站URL"
          />
        </a-form-item>

        <a-form-item label="最大页面限制">
          <a-input-number
            v-decorator="[
              'maxPageLimit',
              {
                rules: [{ required: true, message: '请输入最大页面限制' }],
                initialValue: 100,
              },
            ]"
            :min="1"
            :max="10000"
            placeholder="请输入最大页面限制"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="搜索模板">
          <a-textarea
            v-decorator="['searchTemplate']"
            placeholder="请输入搜索模板（可选）"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="关键词">
          <a-select
            v-decorator="['keywords']"
            mode="tags"
            placeholder="请输入关键词，按回车添加"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="备注">
          <a-textarea
            v-decorator="['remarks']"
            placeholder="请输入备注（可选）"
            :rows="2"
          />
        </a-form-item>

        <a-form-item label="启用状态">
          <a-switch
            v-decorator="[
              'enabled',
              { valuePropName: 'checked', initialValue: true },
            ]"
            checked-children="启用"
            un-checked-children="禁用"
          />
        </a-form-item>
      </a-form>
    </a-modal>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "nuxt-property-decorator";
import moment from "moment";
import {
  CrawlConfigItem,
  CrawlConfigQueryParams,
  AddCrawlConfigParams,
  UpdateCrawlConfigParams,
  CrawlConfigOperationResponse,
} from "~/model/LawyerConfig";

@Component({
  name: "crawl-config-index-component",
  beforeCreate() {
    this.form = this.$form.createForm(this);
  },
})
export default class CrawlConfigIndexComponent extends Vue {
  // 数据状态
  configList: CrawlConfigItem[] = [];
  tableLoading = false;
  searchText = "";
  filterEnabled: number | undefined = undefined;

  // 分页配置
  pagination = {
    current: 1,
    pageSize: 10,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `共 ${total} 条记录`,
  };

  // 模态框状态
  modalVisible = false;
  modalLoading = false;
  editingRecord: CrawlConfigItem | null = null;
  form: any = null;

  // 表格列配置
  columns = [
    {
      title: "网站信息",
      dataIndex: "websiteName",
      key: "websiteName",
      scopedSlots: { customRender: "websiteName" },
      width: 200,
    },
    {
      title: "网站URL",
      dataIndex: "websiteUrl",
      key: "websiteUrl",
      scopedSlots: { customRender: "websiteUrl" },
      ellipsis: true,
    },
    {
      title: "最大页面限制",
      dataIndex: "maxPageLimit",
      key: "maxPageLimit",
      width: 120,
      align: "center",
    },
    {
      title: "启用状态",
      dataIndex: "enabled",
      key: "enabled",
      scopedSlots: { customRender: "enabled" },
      width: 100,
      align: "center",
    },
    {
      title: "创建人",
      dataIndex: "createdBy",
      key: "createdBy",
      width: 100,
    },
    {
      title: "创建时间",
      dataIndex: "createdTime",
      key: "createdTime",
      scopedSlots: { customRender: "createdTime" },
      width: 160,
    },
    {
      title: "操作",
      key: "action",
      scopedSlots: { customRender: "action" },
      width: 120,
      align: "center",
    },
  ];

  get modalTitle(): string {
    return this.editingRecord ? "编辑配置" : "新增配置";
  }

  // 组件挂载时加载数据
  async mounted(): Promise<void> {
    await this.loadConfigList();
  }

  // 加载配置列表
  async loadConfigList(): Promise<void> {
    this.tableLoading = true;
    try {
      const params: CrawlConfigQueryParams = {
        current: this.pagination.current,
        size: this.pagination.pageSize,
        websiteName: this.searchText || undefined,
        websiteCode: this.searchText || undefined,
        enabled: this.filterEnabled,
      };

      const response = await this.$roadLawyerService.getCrawlConfigList(params);

      if (response.success && response.data) {
        this.configList = response.data.records.map((item: any) => ({
          ...item,
          switchLoading: false, // 添加开关加载状态
        }));
        this.pagination.total = response.data.total;
        this.pagination.current = response.data.current;
      } else {
        this.$message.error(response.message || "加载配置列表失败");
        this.configList = [];
        this.pagination.total = 0;
      }
    } catch (error) {
      console.error("加载配置列表失败:", error);
      this.$message.error("加载配置列表失败，请刷新页面重试");
      this.configList = [];
      this.pagination.total = 0;
    } finally {
      this.tableLoading = false;
    }
  }

  // 搜索
  async onSearch(): Promise<void> {
    this.pagination.current = 1;
    await this.loadConfigList();
  }

  // 重置搜索条件
  onReset(): void {
    this.searchText = "";
    this.filterEnabled = undefined;
    this.pagination.current = 1;
    this.loadConfigList();
  }

  // 表格变化处理
  async handleTableChange(pagination: any): Promise<void> {
    this.pagination.current = pagination.current;
    this.pagination.pageSize = pagination.pageSize;
    await this.loadConfigList();
  }

  // 显示新增模态框
  showAddModal(): void {
    this.editingRecord = null;
    this.modalVisible = true;
    this.$nextTick(() => {
      this.form.resetFields();
    });
  }

  // 编辑配置
  editConfig(record: CrawlConfigItem): void {
    this.editingRecord = record;
    this.modalVisible = true;
    this.$nextTick(() => {
      this.form.setFieldsValue({
        websiteName: record.websiteName,
        websiteCode: record.websiteCode,
        websiteUrl: record.websiteUrl,
        maxPageLimit: record.maxPageLimit,
        searchTemplate: record.searchTemplate,
        keywords: record.keywords || [],
        remarks: record.remarks,
        enabled: record.enabled === 1,
      });
    });
  }

  // 切换启用状态
  async toggleEnabled(
    record: CrawlConfigItem,
    checked: boolean
  ): Promise<void> {
    // 设置开关加载状态
    this.$set(record, "switchLoading", true);

    try {
      const params: UpdateCrawlConfigParams = {
        ...record,
        enabled: checked ? 1 : 0,
      };

      const response = await this.$roadLawyerService.updateCrawlConfig(params);

      if (response.success) {
        record.enabled = checked ? 1 : 0;
        this.$message.success(`已${checked ? "启用" : "禁用"}配置`);
      } else {
        this.$message.error(response.message || "操作失败");
      }
    } catch (error) {
      console.error("切换状态失败:", error);
      this.$message.error("操作失败，请重试");
    } finally {
      this.$set(record, "switchLoading", false);
    }
  }

  // 删除配置
  deleteConfig(record: CrawlConfigItem): void {
    this.$confirm({
      title: "确认删除",
      content: `确定要删除配置"${record.websiteName}"吗？此操作不可撤销。`,
      okText: "确认删除",
      okType: "danger",
      cancelText: "取消",
      onOk: async () => {
        try {
          const response = await this.$roadLawyerService.deleteCrawlConfig({
            id: record.id.toString(),
          });

          if (response.success) {
            this.$message.success("删除成功");
            await this.loadConfigList();
          } else {
            this.$message.error(response.message || "删除失败");
          }
        } catch (error) {
          console.error("删除配置失败:", error);
          this.$message.error("删除失败，请重试");
        }
      },
    });
  }

  // 模态框确认
  async handleModalOk(): Promise<void> {
    try {
      const values = await this.form.validateFields();
      this.modalLoading = true;

      const params = {
        websiteName: values.websiteName,
        websiteCode: values.websiteCode,
        websiteUrl: values.websiteUrl,
        maxPageLimit: values.maxPageLimit,
        searchTemplate: values.searchTemplate || "",
        keywords: values.keywords || [],
        remarks: values.remarks || "",
        enabled: values.enabled ? 1 : 0,
        createdBy: "admin", // TODO: 从用户信息获取
      };

      let response: CrawlConfigOperationResponse;
      if (this.editingRecord) {
        // 编辑
        response = await this.$roadLawyerService.updateCrawlConfig({
          ...params,
          id: this.editingRecord.id,
          createdTime: this.editingRecord.createdTime,
          updateTime: new Date().toISOString(),
        } as UpdateCrawlConfigParams);
      } else {
        // 新增
        response = await this.$roadLawyerService.addCrawlConfig(
          params as AddCrawlConfigParams
        );
      }

      if (response.success) {
        this.$message.success(this.editingRecord ? "编辑成功" : "新增成功");
        this.modalVisible = false;
        await this.loadConfigList();
      } else {
        this.$message.error(response.message || "操作失败");
      }
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      console.error("保存配置失败:", error);
      this.$message.error("操作失败，请重试");
    } finally {
      this.modalLoading = false;
    }
  }

  // 模态框取消
  handleModalCancel(): void {
    this.modalVisible = false;
    this.editingRecord = null;
  }

  // 格式化时间
  formatTime(timeStr: string): string {
    if (!timeStr) return "-";
    return moment(timeStr).format("YYYY-MM-DD HH:mm:ss");
  }
}
</script>

<style lang="less" scoped>
.lawyer-search-form {
  margin-bottom: 16px;
}
.lawyer-width-100 {
  width: 100%;
}
.lawyer-search-buttons {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.lawyer-button-group {
  display: flex;
  gap: 8px;
}

.lawyer-table-card {
  .ant-table-wrapper {
    .ant-table-tbody > tr > td {
      padding: 12px 16px;
    }
  }
}

.lawyer-table-title {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
}

.lawyer-table-subtitle {
  font-size: 12px;
  color: #999;
}

.lawyer-action-links {
  display: flex;
  gap: 8px;
  justify-content: center;

  a {
    color: #1890ff;

    &:hover {
      color: #40a9ff;
    }

    &.lawyer-link-delete {
      color: #ff4d4f;

      &:hover {
        color: #ff7875;
      }
    }
  }
}
</style>
